# 基于虾哥编译好的固件配置自定义服务器

## 第1步 确认版本
烧录虾哥已经编译好的[1.6.1版本以上固件](https://github.com/78/xiaozhi-esp32/releases)

## 第2步 准备你的ota地址
如果你按照教程使用的是全模块部署，就应该会有ota地址。

此刻，请你用浏览器打开你的ota地址，例如我的ota地址
```
https://2662r3426b.vicp.fun/xiaozhi/ota/
```

如果显示“OTA接口运行正常，websocket集群数量：X”。那就往下。

如果显示“OTA接口运行不正常”，大概是你还没在`智控台`配置`Websocket`地址。那就：

- 1、使用超级管理员登录智控台

- 2、顶部菜单点击`参数管理`

- 3、在列表中找到`server.websocket`项目，输入你的`Websocket`地址。例如我的就是

```
wss://2662r3426b.vicp.fun/xiaozhi/v1/
```

配置完后，再使用浏览器刷新你的ota接口地址，看看是不是正常了。如果还不正常就，就再次确认一下Websocket是否正常启动，是否配置了Websocket地址。

## 第3步 进入配网模式
进入机器的配网模式，在页面顶部，点击“高级选项”，在里面输入你服务器的`ota`地址，点击保存。重启设备
![请参考-OTA地址设置](../docs/images/firmware-setting-ota.png)

## 第4步 唤醒小智，查看日志输出

唤醒小智，看看日志是不是正常输出。


## 常见问题
以下是一些常见问题，供参考：

[1、为什么我说的话，小智识别出来很多韩文、日文、英文](./FAQ.md)

[2、为什么会出现“TTS 任务出错 文件不存在”？](./FAQ.md)

[3、TTS 经常失败，经常超时](./FAQ.md)

[4、使用Wifi能连接自建服务器，但是4G模式却接不上](./FAQ.md)

[5、如何提高小智对话响应速度？](./FAQ.md)

[6、我说话很慢，停顿时小智老是抢话](./FAQ.md)

[7、我想通过小智控制电灯、空调、远程开关机等操作](./FAQ.md)
