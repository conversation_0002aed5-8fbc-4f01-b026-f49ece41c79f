#!/usr/bin/env python3
"""
火山引擎双流式TTS增强版本
添加完整的语音复刻功能和文本分段处理
"""

import asyncio
import json
import uuid
import aiofiles
import websockets
import pyaudio
import base64
import os
from pathlib import Path
from websockets.asyncio.client import ClientConnection

# 协议常量
PROTOCOL_VERSION = 0b0001
DEFAULT_HEADER_SIZE = 0b0001

# Message Type
FULL_CLIENT_REQUEST = 0b0001
AUDIO_ONLY_RESPONSE = 0b1011
FULL_SERVER_RESPONSE = 0b1001
ERROR_INFORMATION = 0b1111

# Message Type Specific Flags
MsgTypeFlagWithEvent = 0b100

# Message Serialization
NO_SERIALIZATION = 0b0000
JSON = 0b0001

# Message Compression
COMPRESSION_NO = 0b0000

# Event 定义
EVENT_NONE = 0
EVENT_Start_Connection = 1
EVENT_FinishConnection = 2
EVENT_ConnectionStarted = 50
EVENT_ConnectionFailed = 51
EVENT_StartSession = 100
EVENT_FinishSession = 102
EVENT_SessionStarted = 150
EVENT_SessionFinished = 152
EVENT_SessionFailed = 153
EVENT_TaskRequest = 200
EVENT_TTSSentenceStart = 350
EVENT_TTSSentenceEnd = 351
EVENT_TTSResponse = 352

# 音频播放参数
CHUNK = 1024
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 24000


def audio_to_base64(audio_path):
    """将音频文件转换为base64编码"""
    try:
        if not os.path.exists(audio_path):
            print(f"❌ 音频文件不存在: {audio_path}")
            return None
        
        with open(audio_path, 'rb') as f:
            audio_data = f.read()
            return base64.b64encode(audio_data).decode('utf-8')
    except Exception as e:
        print(f"❌ 转换音频文件失败 {audio_path}: {e}")
        return None


def split_text_by_sentences(text, max_length=100):
    """按句号分割文本，支持长文本分段处理"""
    if not text:
        return []
    
    # 句子分隔符
    sentence_endings = ['。', '！', '？', '.', '!', '?']
    
    segments = []
    current_segment = ""
    
    for char in text:
        current_segment += char
        
        # 如果遇到句子结束符或达到最大长度
        if char in sentence_endings or len(current_segment) >= max_length:
            if current_segment.strip():
                segments.append(current_segment.strip())
                current_segment = ""
    
    # 添加剩余的文本
    if current_segment.strip():
        segments.append(current_segment.strip())
    
    return segments


class Header:
    def __init__(self, protocol_version=PROTOCOL_VERSION, header_size=DEFAULT_HEADER_SIZE,
                 message_type: int = 0, message_type_specific_flags: int = 0,
                 serial_method: int = NO_SERIALIZATION, compression_type: int = COMPRESSION_NO,
                 reserved_data=0):
        self.header_size = header_size
        self.protocol_version = protocol_version
        self.message_type = message_type
        self.message_type_specific_flags = message_type_specific_flags
        self.serial_method = serial_method
        self.compression_type = compression_type
        self.reserved_data = reserved_data

    def as_bytes(self) -> bytes:
        return bytes([
            (self.protocol_version << 4) | self.header_size,
            (self.message_type << 4) | self.message_type_specific_flags,
            (self.serial_method << 4) | self.compression_type,
            self.reserved_data
        ])


class Optional:
    def __init__(self, event: int = EVENT_NONE, sessionId: str = None, sequence: int = None):
        self.event = event
        self.sessionId = sessionId
        self.errorCode: int = 0
        self.connectionId: str | None = None
        self.response_meta_json: str | None = None
        self.sequence = sequence

    def as_bytes(self) -> bytes:
        option_bytes = bytearray()
        if self.event != EVENT_NONE:
            option_bytes.extend(self.event.to_bytes(4, "big", signed=True))
        if self.sessionId is not None:
            session_id_bytes = str.encode(self.sessionId)
            size = len(session_id_bytes).to_bytes(4, "big", signed=True)
            option_bytes.extend(size)
            option_bytes.extend(session_id_bytes)
        if self.sequence is not None:
            option_bytes.extend(self.sequence.to_bytes(4, "big", signed=True))
        return option_bytes


class Response:
    def __init__(self, header: Header, optional: Optional):
        self.optional = optional
        self.header = header
        self.payload: bytes | None = None
        self.payload_json: str | None = None


class AudioPlayer:
    def __init__(self, format=FORMAT, channels=CHANNELS, rate=RATE, chunk=CHUNK):
        self.format = format
        self.channels = channels
        self.rate = rate
        self.chunk = chunk
        self.p = pyaudio.PyAudio()
        self.stream = None

    def start_stream(self):
        if self.stream is None:
            self.stream = self.p.open(
                format=self.format,
                channels=self.channels,
                rate=self.rate,
                output=True,
                frames_per_buffer=self.chunk
            )

    def play_audio(self, audio_data):
        if self.stream is None:
            self.start_stream()
        try:
            self.stream.write(audio_data)
        except Exception as e:
            print(f"播放音频时出错: {e}")

    def close(self):
        if self.stream is not None:
            self.stream.stop_stream()
            self.stream.close()
            self.stream = None
        self.p.terminate()


async def send_event(ws: websockets, header: bytes, optional: bytes | None = None, payload: bytes = None):
    """发送事件到WebSocket"""
    full_client_request = bytearray(header)
    if optional is not None:
        full_client_request.extend(optional)
    if payload is not None:
        payload_size = len(payload).to_bytes(4, 'big', signed=True)
        full_client_request.extend(payload_size)
        full_client_request.extend(payload)
    await ws.send(full_client_request)


def read_res_content(res: bytes, offset: int):
    """读取响应内容"""
    content_size = int.from_bytes(res[offset: offset + 4])
    offset += 4
    content = str(res[offset: offset + content_size], encoding='utf8')
    offset += content_size
    return content, offset


def read_res_payload(res: bytes, offset: int):
    """读取响应payload"""
    payload_size = int.from_bytes(res[offset: offset + 4])
    offset += 4
    payload = res[offset: offset + payload_size]
    offset += payload_size
    return payload, offset


def parser_response(res) -> Response:
    """解析WebSocket响应"""
    if isinstance(res, str):
        raise RuntimeError(res)
    
    response = Response(Header(), Optional())
    
    # 解析header
    header = response.header
    num = 0b00001111
    header.protocol_version = res[0] >> 4 & num
    header.header_size = res[0] & 0x0f
    header.message_type = (res[1] >> 4) & num
    header.message_type_specific_flags = res[1] & 0x0f
    header.serialization_method = res[2] >> num
    header.message_compression = res[2] & 0x0f
    header.reserved = res[3]
    
    offset = 4
    optional = response.optional
    
    if header.message_type == FULL_SERVER_RESPONSE or AUDIO_ONLY_RESPONSE:
        if header.message_type_specific_flags == MsgTypeFlagWithEvent:
            optional.event = int.from_bytes(res[offset:8])
            offset += 4
            if optional.event == EVENT_NONE:
                return response
            elif optional.event == EVENT_ConnectionStarted:
                optional.connectionId, offset = read_res_content(res, offset)
            elif optional.event == EVENT_ConnectionFailed:
                optional.response_meta_json, offset = read_res_content(res, offset)
            elif (optional.event == EVENT_SessionStarted or 
                  optional.event == EVENT_SessionFailed or 
                  optional.event == EVENT_SessionFinished):
                optional.sessionId, offset = read_res_content(res, offset)
                optional.response_meta_json, offset = read_res_content(res, offset)
            elif optional.event == EVENT_TTSResponse:
                optional.sessionId, offset = read_res_content(res, offset)
                response.payload, offset = read_res_payload(res, offset)
            elif optional.event == EVENT_TTSSentenceEnd or optional.event == EVENT_TTSSentenceStart:
                optional.sessionId, offset = read_res_content(res, offset)
                response.payload_json, offset = read_res_content(res, offset)
    
    elif header.message_type == ERROR_INFORMATION:
        optional.errorCode = int.from_bytes(res[offset:offset + 4], "big", signed=True)
        offset += 4
        response.payload, offset = read_res_payload(res, offset)
        if response.payload:
            try:
                error_msg = response.payload.decode('utf-8')
                response.payload_json = error_msg
                print(f"❌ 服务器错误信息: {error_msg}")
            except:
                print(f"❌ 服务器错误码: {optional.errorCode}")
    
    return response


def get_payload_bytes(uid='1234',
                      event=EVENT_NONE,
                      text='',
                      speaker='',
                     audio_format='pcm',
                    audio_sample_rate=24000,
                     use_clone_speaker=False,
                      clone_speaker_id=None,
                      post_process=None,
                     reference_audio_list=None,
                      reference_text_list=None
                      ):
    """生成请求payload，支持语音复刻功能"""
    req_params = {
        "text": text,
        "audio_params": {
            "format": audio_format,
            "sample_rate": audio_sample_rate,
        }
    }

    # 设置发音人和后处理参数
    if use_clone_speaker and clone_speaker_id:
        req_params["speaker"] = clone_speaker_id

        # 添加语音复刻参数
        if reference_audio_list and reference_text_list:
            try:
                voice_cloning_data = []
                for i, audio_path in enumerate(reference_audio_list):
                    if i < len(reference_text_list):
                        audio_base64 = audio_to_base64(audio_path)
                        ref_text = reference_text_list[i]

                        if audio_base64 and ref_text:
                            voice_cloning_data.append({
                                "audio": audio_base64,
                                "text": ref_text
                            })
                            print(f"✅ 添加语音复刻参考 {i+1}: {ref_text[:30]}...")

                if voice_cloning_data:
                    req_params["voice_cloning"] = {
                        "enable": True,
                        "references": voice_cloning_data
                    }
                    print(f"🎤 语音复刻已启用，参考音频数量: {len(voice_cloning_data)}")
                else:
                    print("⚠️ 语音复刻已启用但没有有效的参考音频")
            except Exception as e:
                print(f"❌ 处理语音复刻参数失败: {e}")

        # 后处理参数 - 复刻发音人使用additions字段
        if post_process:
            req_params["additions"] = json.dumps({"post_process": post_process})
        else:
            req_params["additions"] = json.dumps({"post_process": {"pitch": 0}})
    elif speaker:
        req_params["speaker"] = speaker
        # 预置发音人使用audio_params中的post_process
        if post_process:
            req_params["audio_params"]["post_process"] = post_process
        else:
            req_params["audio_params"]["post_process"] = {"pitch": 0}

    payload = {
        "user": {"uid": uid},
        "event": event,
        "namespace": "BidirectionalTTS",
        "req_params": req_params,
    }

    return str.encode(json.dumps(payload))


async def start_connection(websocket):
    """开始连接"""
    header = Header(message_type=FULL_CLIENT_REQUEST, message_type_specific_flags=MsgTypeFlagWithEvent).as_bytes()
    optional = Optional(event=EVENT_Start_Connection).as_bytes()
    payload = str.encode("{}")
    return await send_event(websocket, header, optional, payload)


async def start_session(websocket, speaker, session_id, use_clone_speaker=False,
                       clone_speaker_id=None, post_process=None,
                       reference_audio_list=None, reference_text_list=None):
    """开始会话"""
    header = Header(message_type=FULL_CLIENT_REQUEST,
                    message_type_specific_flags=MsgTypeFlagWithEvent,
                    serial_method=JSON).as_bytes()
    optional = Optional(event=EVENT_StartSession, sessionId=session_id).as_bytes()
    payload = get_payload_bytes(
        event=EVENT_StartSession,
        speaker=speaker,
        use_clone_speaker=use_clone_speaker,
        clone_speaker_id=clone_speaker_id,
        post_process=post_process,
        reference_audio_list=reference_audio_list,
        reference_text_list=reference_text_list
    )
    return await send_event(websocket, header, optional, payload)


async def send_text(ws: ClientConnection, speaker: str, text: str, session_id,
                   use_clone_speaker=False, clone_speaker_id=None, post_process=None,
                   reference_audio_list=None, reference_text_list=None):
    """发送文本"""
    header = Header(message_type=FULL_CLIENT_REQUEST,
                    message_type_specific_flags=MsgTypeFlagWithEvent,
                    serial_method=JSON).as_bytes()
    optional = Optional(event=EVENT_TaskRequest, sessionId=session_id).as_bytes()
    payload = get_payload_bytes(
        event=EVENT_TaskRequest,
        text=text,
        speaker=speaker,
        use_clone_speaker=use_clone_speaker,
        clone_speaker_id=clone_speaker_id,
        post_process=post_process,
        reference_audio_list=reference_audio_list,
        reference_text_list=reference_text_list
    )
    return await send_event(ws, header, optional, payload)


async def finish_session(ws, session_id):
    """结束会话"""
    header = Header(message_type=FULL_CLIENT_REQUEST,
                    message_type_specific_flags=MsgTypeFlagWithEvent,
                    serial_method=JSON).as_bytes()
    optional = Optional(event=EVENT_FinishSession, sessionId=session_id).as_bytes()
    payload = str.encode('{}')
    return await send_event(ws, header, optional, payload)


async def finish_connection(ws):
    """结束连接"""
    header = Header(message_type=FULL_CLIENT_REQUEST,
                    message_type_specific_flags=MsgTypeFlagWithEvent,
                    serial_method=JSON).as_bytes()
    optional = Optional(event=EVENT_FinishConnection).as_bytes()
    payload = str.encode('{}')
    return await send_event(ws, header, optional, payload)


def print_response(res, tag: str):
    """打印响应信息"""
    print(f'===>{tag} event:{res.optional.event}')
    print(f'===>{tag} payload len:{0 if res.payload is None else len(res.payload)}')
    if res.payload_json:
        print(f'===>{tag} payload_json:{res.payload_json}')


async def run_enhanced_tts(app_id: str, token: str, speaker: str, text: str, output_path: str,
                          stream_audio=False, use_clone_speaker=False, clone_speaker_id=None,
                          post_process=None, reference_audio_list=None, reference_text_list=None,
                          split_long_text=True, max_segment_length=100):
    """
    运行增强版双流式TTS，支持语音复刻和文本分段

    Args:
        app_id: 火山引擎应用ID
        token: 访问令牌
        speaker: 发音人ID（预置发音人）
        text: 要合成的文本
        output_path: 输出音频文件路径
        stream_audio: 是否实时播放音频
        use_clone_speaker: 是否使用复刻发音人
        clone_speaker_id: 复刻发音人ID
        post_process: 后处理参数
        reference_audio_list: 参考音频文件列表
        reference_text_list: 参考音频对应的文本列表
        split_long_text: 是否分割长文本
        max_segment_length: 文本分段的最大长度
    """

    # 根据是否使用复刻发音人选择Resource ID
    if use_clone_speaker:
        resource_id = 'volc.megatts.default'  # 复刻发音人
    else:
        resource_id = 'volc.service_type.10029'  # 大模型语音合成

    ws_header = {
        "X-Api-App-Key": app_id,
        "X-Api-Access-Key": token,
        "X-Api-Resource-Id": resource_id,
        "X-Api-Connect-Id": str(uuid.uuid4()),
    }

    url = 'wss://openspeech.bytedance.com/api/v3/tts/bidirection'

    print(f"🔗 连接信息:")
    print(f"  URL: {url}")
    print(f"  App ID: {app_id}")
    print(f"  Resource ID: {resource_id}")
    if use_clone_speaker:
        print(f"  复刻发音人ID: {clone_speaker_id}")
        if reference_audio_list:
            print(f"  参考音频数量: {len(reference_audio_list)}")
    else:
        print(f"  预置发音人: {speaker}")

    # 文本分段处理
    if split_long_text and len(text) > max_segment_length:
        text_segments = split_text_by_sentences(text, max_segment_length)
        print(f"📝 长文本已分割为 {len(text_segments)} 段")
    else:
        text_segments = [text]

    # 初始化音频播放器
    audio_player = None
    if stream_audio:
        audio_player = AudioPlayer()

    try:
        async with websockets.connect(url, extra_headers=ws_header, max_size=1000000000,
                                    ping_interval=30, ping_timeout=10) as ws:

            # 1. 开始连接
            print("📡 发送start_connection请求...")
            await start_connection(ws)
            res = parser_response(await ws.recv())
            print_response(res, 'start_connection response:')

            if res.optional.event != EVENT_ConnectionStarted:
                error_msg = f"连接建立失败，事件码: {res.optional.event}"
                if res.payload_json:
                    error_msg += f", 错误信息: {res.payload_json}"
                raise RuntimeError(error_msg)

            # 2. 开始会话
            session_id = uuid.uuid4().__str__().replace('-', '')
            print(f"📋 发送start_session请求 (session_id: {session_id})...")
            await start_session(ws, speaker, session_id, use_clone_speaker, clone_speaker_id,
                               post_process, reference_audio_list, reference_text_list)
            res = parser_response(await ws.recv())
            print_response(res, 'start_session response:')

            if res.optional.event != EVENT_SessionStarted:
                error_msg = f"会话建立失败，事件码: {res.optional.event}"
                if res.payload_json:
                    error_msg += f", 错误信息: {res.payload_json}"
                raise RuntimeError(error_msg)

            # 3. 处理文本分段并接收音频
            async with aiofiles.open(output_path, mode="wb") as output_file:
                for i, segment in enumerate(text_segments):
                    print(f"📝 发送文本段 {i+1}/{len(text_segments)}: {segment[:50]}{'...' if len(segment) > 50 else ''}")
                    await send_text(ws, speaker, segment, session_id, use_clone_speaker,
                                   clone_speaker_id, post_process, reference_audio_list, reference_text_list)

                    print(f"🎵 接收音频数据段 {i+1}...")
                    segment_audio_received = False

                    while not segment_audio_received:
                        res = parser_response(await ws.recv())
                        print_response(res, f'audio response segment {i+1}:')

                        if res.optional.event == EVENT_TTSResponse and res.header.message_type == AUDIO_ONLY_RESPONSE:
                            if res.payload:
                                await output_file.write(res.payload)
                                if stream_audio and audio_player:
                                    audio_player.play_audio(res.payload)
                        elif res.optional.event == EVENT_TTSSentenceEnd:
                            segment_audio_received = True
                            print(f"✅ 文本段 {i+1} 处理完成")
                        elif res.optional.event in [EVENT_TTSSentenceStart]:
                            continue
                        else:
                            # 其他事件，继续处理
                            continue

                # 4. 结束会话
                print("🔚 发送finish_session请求...")
                await finish_session(ws, session_id)

            # 5. 结束连接
            await finish_connection(ws)
            res = parser_response(await ws.recv())
            print_response(res, 'finish_connection response:')

            print('✅ 合成完成')

    except Exception as e:
        print(f"❌ 错误: {e}")
        raise
    finally:
        if audio_player:
            audio_player.close()


async def demo_preset_speaker():
    """演示预置发音人"""
    print("🎤 演示预置发音人...")
    await run_enhanced_tts(
        app_id='1181316113',
        token='XliULxhgl1UU0zUkMuuTjzcJkOyryWLA',
        speaker='zh_female_wanwanxiaohe_moon_bigtts',
        text='这是使用预置发音人生成的语音。今天天气真不错，适合出门走走。感谢您的使用，祝您生活愉快。',
        output_path='/Users/<USER>/Downloads/preset_speaker.wav',
        stream_audio=True,
        use_clone_speaker=False,
        split_long_text=True,
        max_segment_length=50
    )


async def demo_clone_speaker():
    """演示复刻发音人"""
    print("🎤 演示复刻发音人...")

    # 参考音频和对应文本（请根据实际情况修改路径）
    reference_audio_list = [
        '/Users/<USER>/Downloads/reference1.wav',
        '/Users/<USER>/Downloads/reference2.wav'
    ]

    reference_text_list = [
        '你好，我是小智助手，很高兴为您服务。',
        '今天天气真不错，适合出门走走。'
    ]

    await run_enhanced_tts(
        app_id='1181316113',
        token='XliULxhgl1UU0zUkMuuTjzcJkOyryWLA',
        speaker='',  # 复刻发音人时可以为空
        text='这是使用声音复刻技术生成的语音。通过分析参考音频，我们可以复制出相似的声音特征。这项技术在语音合成领域具有重要意义。',
        output_path='/Users/<USER>/Downloads/clone_speaker.wav',
        stream_audio=True,
        use_clone_speaker=True,
        clone_speaker_id='S_Faev7OGv1',  # 你的复刻发音人ID
        post_process={"pitch": 0},
        reference_audio_list=reference_audio_list,
        reference_text_list=reference_text_list,
        split_long_text=True,
        max_segment_length=60
    )


async def demo_long_text():
    """演示长文本分段处理"""
    print("📝 演示长文本分段处理...")

    long_text = """
    人工智能技术的发展日新月异，语音合成作为其中的重要分支，已经在各个领域得到了广泛应用。
    从最初的机械式合成到现在的神经网络驱动，语音合成技术经历了巨大的变革。
    现代的语音合成系统不仅能够产生自然流畅的语音，还能够模拟不同的说话风格和情感表达。
    语音复刻技术更是将这一领域推向了新的高度，通过少量的参考音频就能够复制出特定的声音特征。
    这项技术在个性化语音助手、有声读物制作、影视配音等领域都有着巨大的应用潜力。
    """

    await run_enhanced_tts(
        app_id='1181316113',
        token='XliULxhgl1UU0zUkMuuTjzcJkOyryWLA',
        speaker='zh_female_wanwanxiaohe_moon_bigtts',
        text=long_text.strip(),
        output_path='/Users/<USER>/Downloads/long_text.wav',
        stream_audio=True,
        use_clone_speaker=False,
        split_long_text=True,
        max_segment_length=80
    )


if __name__ == "__main__":
    print("🎤 火山引擎双流式TTS增强版演示")
    print("=" * 60)

    try:
        # 选择演示模式
        print("\n请选择演示模式:")
        print("1. 预置发音人")
        print("2. 复刻发音人")
        print("3. 长文本分段处理")
        print("4. 全部演示")

        choice = input("\n请输入选择 (1-4): ").strip()

        if choice == "1":
            asyncio.run(demo_preset_speaker())
        elif choice == "2":
            asyncio.run(demo_clone_speaker())
        elif choice == "3":
            asyncio.run(demo_long_text())
        elif choice == "4":
            print("\n🚀 开始全部演示...")
            asyncio.run(demo_preset_speaker())
            print("\n" + "="*60)
            asyncio.run(demo_clone_speaker())
            print("\n" + "="*60)
            asyncio.run(demo_long_text())
        else:
            print("❌ 无效选择，默认运行复刻发音人演示")
            asyncio.run(demo_clone_speaker())

        print("\n✅ 演示完成！")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()
