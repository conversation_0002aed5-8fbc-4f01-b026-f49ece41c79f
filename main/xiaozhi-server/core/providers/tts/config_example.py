#!/usr/bin/env python3
"""
火山引擎双流式TTS配置示例
"""

# 基本配置
TTS_CONFIG = {
    # 火山引擎基本信息
    "app_id": "你的火山引擎应用ID",
    "access_token": "你的火山引擎访问令牌",
    
    # WebSocket连接地址
    "ws_url": "wss://openspeech.bytedance.com/api/v3/tts/bidirection",
    
    # 预置发音人配置
    "preset_speaker": {
        "speaker": "zh_female_wanwanxiaohe_moon_bigtts",  # 预置发音人ID
        "resource_id": "volc.service_type.10029",  # 大模型语音合成
        "post_process": {
            "pitch": 0,  # 音调调整
            "speed": 1.0,  # 语速调整
            "volume": 1.0  # 音量调整
        }
    },
    
    # 语音复刻配置
    "voice_cloning": {
        "enabled": True,
        "clone_speaker_id": "你的复刻发音人ID",  # 例如: S_Faev7OGv1
        "resource_id": "volc.megatts.default",  # 复刻发音人专用
        
        # 参考音频配置
        "reference_audio": [
            "/path/to/reference1.wav",
            "/path/to/reference2.wav",
            "/path/to/reference3.wav"
        ],
        
        # 参考音频对应的文本（必须与音频内容完全一致）
        "reference_text": [
            "你好，我是小智助手，很高兴为您服务。",
            "今天天气真不错，适合出门走走。",
            "感谢您的使用，祝您生活愉快。"
        ],
        
        # 后处理参数
        "post_process": {
            "pitch": 0,  # 音调调整
            "speed": 1.0,  # 语速调整
            "volume": 1.0  # 音量调整
        }
    },
    
    # 音频输出配置
    "audio_config": {
        "format": "pcm",  # 音频格式
        "sample_rate": 24000,  # 采样率
        "channels": 1,  # 声道数
        "output_dir": "/Users/<USER>/Downloads/"  # 输出目录
    },
    
    # 文本处理配置
    "text_config": {
        "split_long_text": True,  # 是否分割长文本
        "max_segment_length": 100,  # 文本分段最大长度
        "sentence_endings": ["。", "！", "？", ".", "!", "?"]  # 句子结束符
    },
    
    # 播放配置
    "playback_config": {
        "stream_audio": True,  # 是否实时播放
        "chunk_size": 1024,  # 音频块大小
        "buffer_size": 4096  # 缓冲区大小
    }
}

# 使用示例
USAGE_EXAMPLES = {
    "preset_speaker": {
        "description": "使用预置发音人",
        "config": {
            "use_clone_speaker": False,
            "speaker": TTS_CONFIG["preset_speaker"]["speaker"],
            "post_process": TTS_CONFIG["preset_speaker"]["post_process"]
        }
    },
    
    "voice_cloning": {
        "description": "使用语音复刻",
        "config": {
            "use_clone_speaker": True,
            "clone_speaker_id": TTS_CONFIG["voice_cloning"]["clone_speaker_id"],
            "reference_audio_list": TTS_CONFIG["voice_cloning"]["reference_audio"],
            "reference_text_list": TTS_CONFIG["voice_cloning"]["reference_text"],
            "post_process": TTS_CONFIG["voice_cloning"]["post_process"]
        }
    }
}

# 错误码说明
ERROR_CODES = {
    "connection_errors": {
        51: "连接失败 - 可能是权限认证问题",
        52: "连接结束",
    },
    
    "session_errors": {
        153: "会话失败 - 检查参数配置",
        152: "会话正常结束"
    },
    
    "common_solutions": [
        "检查app_id和access_token是否正确",
        "确认网络连接正常",
        "验证发音人ID是否有效",
        "检查参考音频文件是否存在且格式正确",
        "确保参考文本与音频内容完全一致"
    ]
}

# 支持的音频格式
SUPPORTED_FORMATS = {
    "input": ["wav", "mp3", "flac", "m4a"],  # 参考音频支持的格式
    "output": ["pcm", "wav", "mp3"]  # 输出音频支持的格式
}

# 发音人列表（部分示例）
AVAILABLE_SPEAKERS = {
    "female": [
        "zh_female_wanwanxiaohe_moon_bigtts",
        "zh_female_shuangkuaisisi_moon_bigtts",
        "zh_female_tianmei_moon_bigtts"
    ],
    "male": [
        "zh_male_jingqiangxiaoming_moon_bigtts",
        "zh_male_zhuangzhongxiaoming_moon_bigtts"
    ]
}

if __name__ == "__main__":
    print("🔧 火山引擎双流式TTS配置示例")
    print("=" * 50)
    
    print("\n📋 基本配置:")
    for key, value in TTS_CONFIG.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for sub_key, sub_value in value.items():
                print(f"    {sub_key}: {sub_value}")
        else:
            print(f"  {key}: {value}")
    
    print("\n🎯 使用示例:")
    for example_name, example_config in USAGE_EXAMPLES.items():
        print(f"  {example_name}: {example_config['description']}")
    
    print("\n⚠️ 注意事项:")
    print("  1. 请替换配置中的占位符为实际值")
    print("  2. 确保参考音频文件路径正确")
    print("  3. 参考文本必须与音频内容完全一致")
    print("  4. 复刻发音人需要先在火山引擎控制台创建")
