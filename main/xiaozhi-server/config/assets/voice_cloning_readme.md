# 火山引擎语音复刻功能说明

## 功能概述

火山引擎双流式TTS现在支持语音复刻功能，可以通过提供参考音频和对应文本来复刻特定的声音特征。

## 配置说明

### 基本配置

在 `config.yaml` 中的 `HuoshanDoubleStreamTTS` 配置项中添加以下参数：

```yaml
HuoshanDoubleStreamTTS:
  type: huoshan_double_stream
  ws_url: wss://openspeech.bytedance.com/api/v3/tts/bidirection
  appid: 你的火山引擎语音合成服务appid
  access_token: 你的火山引擎语音合成服务access_token
  resource_id: volc.service_type.10029
  speaker: zh_female_wanwan<PERSON>ohe_moon_bigtts
  
  # 语音复刻功能配置
  voice_cloning: true  # 启用语音复刻
  reference_audio: 
    - "config/assets/reference_voice_1.wav"
    - "config/assets/reference_voice_2.wav"
  reference_text: 
    - "这是第一段参考音频对应的文本内容"
    - "这是第二段参考音频对应的文本内容"
```

### 参数说明

- `voice_cloning`: 布尔值，是否启用语音复刻功能
- `reference_audio`: 字符串数组，参考音频文件的路径列表
- `reference_text`: 字符串数组，参考音频对应的文本内容列表

## 音频文件要求

### 格式要求
- 支持格式：WAV、MP3、FLAC等常见音频格式
- 推荐格式：WAV (16kHz, 16bit, 单声道)
- 文件大小：建议每个文件不超过10MB

### 质量要求
- 音频清晰，无明显噪音
- 语速适中，发音清晰
- 建议录制时长：每段3-10秒
- 建议提供2-5段不同内容的参考音频

### 文本要求
- 参考文本必须与音频内容完全一致
- 支持中文、英文等多种语言
- 文本长度建议与音频时长匹配

## 使用示例

### 1. 准备参考音频
将参考音频文件放置在 `config/assets/` 目录下：
```
config/assets/
├── reference_voice_1.wav
├── reference_voice_2.wav
└── reference_voice_3.wav
```

### 2. 配置参考文本
确保每个音频文件都有对应的文本：
```yaml
reference_audio: 
  - "config/assets/reference_voice_1.wav"
  - "config/assets/reference_voice_2.wav"
  - "config/assets/reference_voice_3.wav"
reference_text: 
  - "你好，我是小智助手"
  - "今天天气真不错"
  - "很高兴为您服务"
```

### 3. 启用功能
设置 `voice_cloning: true` 并重启服务。

## 注意事项

1. **版权合规**: 请确保使用的参考音频具有合法的使用权限
2. **隐私保护**: 不要使用他人的音频进行未经授权的语音复刻
3. **质量影响**: 参考音频的质量直接影响复刻效果
4. **性能考虑**: 启用语音复刻会增加请求处理时间
5. **成本影响**: 语音复刻功能可能产生额外的API调用费用

## 故障排除

### 常见问题

1. **音频文件读取失败**
   - 检查文件路径是否正确
   - 确认文件格式是否支持
   - 验证文件权限是否正确

2. **文本不匹配**
   - 确保参考文本与音频内容完全一致
   - 检查文本编码是否为UTF-8

3. **复刻效果不佳**
   - 提供更多高质量的参考音频
   - 确保参考音频的多样性
   - 检查音频质量和清晰度

### 日志查看
启用语音复刻后，可以在日志中查看相关信息：
```
[INFO] 添加语音复刻参考: config/assets/reference_voice_1.wav -> 你好，我是小智助手...
[INFO] 启用语音复刻，参考音频数量: 3
```

## 技术支持

如遇到问题，请查看：
1. 火山引擎官方文档：https://www.volcengine.com/docs/6561/1329505
2. 项目GitHub Issues
3. 相关日志文件
