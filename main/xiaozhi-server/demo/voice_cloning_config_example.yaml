# 火山引擎语音复刻功能配置示例
# 复制此配置到你的 config.yaml 中的 HuoshanDoubleStreamTTS 部分

HuoshanDoubleStreamTTS:
  type: huoshan_double_stream
  
  # 基本配置 - 请替换为你的实际信息
  ws_url: wss://openspeech.bytedance.com/api/v3/tts/bidirection
  appid: "你的火山引擎语音合成服务appid"
  access_token: "你的火山引擎语音合成服务access_token"
  resource_id: "volc.service_type.10029"
  speaker: "zh_female_wanwanxiaohe_moon_bigtts"
  
  # 语音复刻功能配置
  voice_cloning: true  # 设置为 true 启用语音复刻
  
  # 参考音频文件路径列表（支持相对路径和绝对路径）
  reference_audio:
    - "config/assets/reference_voice_1.wav"
    - "config/assets/reference_voice_2.wav"
    - "config/assets/reference_voice_3.wav"
  
  # 参考音频对应的文本内容（必须与音频内容完全一致）
  reference_text:
    - "你好，我是小智助手，很高兴为您服务。"
    - "今天天气真不错，适合出门走走。"
    - "感谢您的使用，祝您生活愉快。"

# 注意事项：
# 1. reference_audio 和 reference_text 的数量必须一致
# 2. 音频文件建议使用 WAV 格式，16kHz 采样率
# 3. 每段音频建议 3-10 秒，内容清晰无噪音
# 4. 文本内容必须与音频完全匹配
# 5. 请确保有合法的音频使用权限

# 禁用语音复刻的配置示例：
# HuoshanDoubleStreamTTS:
#   type: huoshan_double_stream
#   ws_url: wss://openspeech.bytedance.com/api/v3/tts/bidirection
#   appid: "你的火山引擎语音合成服务appid"
#   access_token: "你的火山引擎语音合成服务access_token"
#   resource_id: "volc.service_type.10029"
#   speaker: "zh_female_wanwanxiaohe_moon_bigtts"
#   voice_cloning: false  # 禁用语音复刻
