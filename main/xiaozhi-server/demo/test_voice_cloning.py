#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音复刻功能单元测试
作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import json
import base64
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.providers.tts.huoshan_double_stream import TTSProvider

def create_test_audio_file():
    """创建测试用的音频文件（模拟）"""
    test_audio_dir = project_root / "demo" / "assets"
    test_audio_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建一个模拟的WAV文件头（用于测试）
    # 这不是真正的音频文件，只是用于测试文件读取功能
    wav_header = b'RIFF\x24\x08\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x40\x1f\x00\x00\x80\x3e\x00\x00\x02\x00\x10\x00data\x00\x08\x00\x00'
    test_data = b'\x00\x00' * 1000  # 模拟音频数据
    
    test_file = test_audio_dir / "test_reference.wav"
    with open(test_file, "wb") as f:
        f.write(wav_header + test_data)
    
    return str(test_file)

def test_audio_to_base64():
    """测试音频文件转base64功能"""
    print("🧪 测试音频文件转base64功能...")
    
    # 创建测试音频文件
    test_file = create_test_audio_file()
    
    # 创建TTS提供者实例
    config = {
        "voice_cloning": True,
        "reference_audio": [test_file],
        "reference_text": ["测试文本"]
    }
    
    tts_provider = TTSProvider(config, delete_audio_file=False)
    
    # 测试音频转base64
    base64_data = tts_provider._audio_to_base64(test_file)
    
    if base64_data:
        print(f"✅ 音频转base64成功，数据长度: {len(base64_data)}")
        print(f"   Base64前缀: {base64_data[:50]}...")
        return True
    else:
        print("❌ 音频转base64失败")
        return False

def test_ref_text_reading():
    """测试参考文本读取功能"""
    print("\n🧪 测试参考文本读取功能...")
    
    # 创建测试文本文件
    test_text_dir = project_root / "demo" / "assets"
    test_text_file = test_text_dir / "test_reference.txt"
    
    test_content = "这是一个测试参考文本内容"
    with open(test_text_file, "w", encoding="utf-8") as f:
        f.write(test_content)
    
    # 创建TTS提供者实例
    config = {"voice_cloning": True}
    tts_provider = TTSProvider(config, delete_audio_file=False)
    
    # 测试从文件读取
    file_content = tts_provider._read_ref_text(str(test_text_file))
    print(f"✅ 从文件读取: {file_content}")
    
    # 测试直接文本
    direct_text = "直接输入的文本"
    direct_content = tts_provider._read_ref_text(direct_text)
    print(f"✅ 直接文本: {direct_content}")
    
    return file_content == test_content and direct_content == direct_text

def test_payload_generation():
    """测试payload生成功能"""
    print("\n🧪 测试payload生成功能...")
    
    # 创建测试文件
    test_audio_file = create_test_audio_file()
    
    # 测试配置
    config = {
        "voice_cloning": True,
        "reference_audio": [test_audio_file],
        "reference_text": ["你好，这是测试音频"]
    }
    
    tts_provider = TTSProvider(config, delete_audio_file=False)
    
    # 生成payload
    payload_bytes = tts_provider.get_payload_bytes(
        event=200,  # EVENT_TaskRequest
        text="测试语音合成文本",
        speaker="test_speaker"
    )
    
    # 解析payload
    try:
        payload_json = json.loads(payload_bytes.decode('utf-8'))
        print("✅ Payload生成成功")
        print(f"   事件类型: {payload_json.get('event')}")
        print(f"   命名空间: {payload_json.get('namespace')}")
        
        req_params = payload_json.get('req_params', {})
        print(f"   文本: {req_params.get('text')}")
        print(f"   音色: {req_params.get('speaker')}")
        
        # 检查语音复刻参数
        voice_cloning = req_params.get('voice_cloning')
        if voice_cloning:
            print(f"   语音复刻启用: {voice_cloning.get('enable')}")
            references = voice_cloning.get('references', [])
            print(f"   参考音频数量: {len(references)}")
            
            for i, ref in enumerate(references):
                print(f"     参考 {i+1}: 文本='{ref.get('text')}', 音频长度={len(ref.get('audio', ''))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Payload解析失败: {str(e)}")
        return False

def test_config_validation():
    """测试配置验证功能"""
    print("\n🧪 测试配置验证功能...")
    
    test_cases = [
        {
            "name": "正常配置",
            "config": {
                "voice_cloning": True,
                "reference_audio": ["demo/assets/test_reference.wav"],
                "reference_text": ["测试文本"]
            },
            "should_work": True
        },
        {
            "name": "禁用语音复刻",
            "config": {
                "voice_cloning": False,
                "reference_audio": [],
                "reference_text": []
            },
            "should_work": True
        },
        {
            "name": "缺少参考音频",
            "config": {
                "voice_cloning": True,
                "reference_audio": [],
                "reference_text": ["测试文本"]
            },
            "should_work": False
        },
        {
            "name": "音频文本数量不匹配",
            "config": {
                "voice_cloning": True,
                "reference_audio": ["audio1.wav", "audio2.wav"],
                "reference_text": ["文本1"]
            },
            "should_work": False
        }
    ]
    
    for test_case in test_cases:
        print(f"\n   测试案例: {test_case['name']}")
        try:
            tts_provider = TTSProvider(test_case['config'], delete_audio_file=False)
            
            # 尝试生成payload
            payload_bytes = tts_provider.get_payload_bytes(
                text="测试文本",
                speaker="test_speaker"
            )
            
            if test_case['should_work']:
                print(f"   ✅ 按预期工作")
            else:
                print(f"   ⚠️  应该失败但成功了")
                
        except Exception as e:
            if not test_case['should_work']:
                print(f"   ✅ 按预期失败: {str(e)}")
            else:
                print(f"   ❌ 意外失败: {str(e)}")

def main():
    """主测试函数"""
    print("🧪 火山引擎语音复刻功能单元测试")
    print("=" * 50)
    
    tests = [
        test_audio_to_base64,
        test_ref_text_reading,
        test_payload_generation,
        test_config_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 发生异常: {str(e)}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查代码")
    
    # 清理测试文件
    try:
        test_dir = project_root / "demo" / "assets"
        for test_file in ["test_reference.wav", "test_reference.txt"]:
            file_path = test_dir / test_file
            if file_path.exists():
                file_path.unlink()
        print("\n🧹 测试文件已清理")
    except Exception as e:
        print(f"⚠️  清理测试文件时出错: {str(e)}")

if __name__ == "__main__":
    main()
