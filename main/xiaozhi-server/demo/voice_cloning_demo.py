#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山引擎语音复刻功能演示
作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import asyncio
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.providers.tts.huoshan_double_stream import TTSProvider
from config.logger import setup_logging

logger = setup_logging()

class VoiceCloningDemo:
    """语音复刻功能演示类"""
    
    def __init__(self):
        self.demo_config = {
            # 基本配置
            "type": "huoshan_double_stream",
            "ws_url": "wss://openspeech.bytedance.com/api/v3/tts/bidirection",
            "appid": "你的火山引擎语音合成服务appid",
            "access_token": "你的火山引擎语音合成服务access_token",
            "resource_id": "volc.service_type.10029",
            "speaker": "zh_female_wanwan<PERSON>ohe_moon_bigtts",
            
            # 语音复刻配置
            "voice_cloning": True,
            "reference_audio": [
                "demo/assets/reference_voice_1.wav",
                "demo/assets/reference_voice_2.wav"
            ],
            "reference_text": [
                "你好，我是小智助手，很高兴为您服务。",
                "今天天气真不错，适合出门走走。"
            ]
        }
        
        # 创建演示资源目录
        self.assets_dir = project_root / "demo" / "assets"
        self.assets_dir.mkdir(parents=True, exist_ok=True)
        
    def create_demo_assets(self):
        """创建演示所需的资源文件"""
        print("🎯 创建演示资源文件...")
        
        # 创建示例音频文件说明
        readme_content = """# 语音复刻演示资源

## 音频文件要求

请将您的参考音频文件放置在此目录下，文件名为：
- reference_voice_1.wav
- reference_voice_2.wav

### 音频要求：
1. 格式：WAV (推荐 16kHz, 16bit, 单声道)
2. 时长：3-10秒
3. 质量：清晰无噪音
4. 内容：与配置中的reference_text对应

### 示例文本：
- reference_voice_1.wav 对应文本："你好，我是小智助手，很高兴为您服务。"
- reference_voice_2.wav 对应文本："今天天气真不错，适合出门走走。"

## 注意事项
- 请确保音频文件具有合法使用权限
- 音频内容必须与文本完全一致
- 建议使用高质量录音设备
"""
        
        readme_path = self.assets_dir / "README.md"
        with open(readme_path, "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print(f"✅ 已创建说明文件: {readme_path}")
        
        # 检查音频文件是否存在
        for i, audio_file in enumerate(self.demo_config["reference_audio"], 1):
            audio_path = project_root / audio_file
            if not audio_path.exists():
                print(f"⚠️  参考音频文件不存在: {audio_path}")
                print(f"   请根据 {readme_path} 的说明准备音频文件")
            else:
                print(f"✅ 找到参考音频文件 {i}: {audio_path}")
    
    def validate_config(self):
        """验证配置是否正确"""
        print("🔍 验证配置...")
        
        # 检查必要的配置项
        required_fields = ["appid", "access_token", "ws_url", "resource_id"]
        missing_fields = []
        
        for field in required_fields:
            if not self.demo_config.get(field) or self.demo_config[field].startswith("你的"):
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少必要配置: {', '.join(missing_fields)}")
            print("   请在demo_config中填入正确的火山引擎API信息")
            return False
        
        # 检查语音复刻配置
        if self.demo_config.get("voice_cloning"):
            ref_audio = self.demo_config.get("reference_audio", [])
            ref_text = self.demo_config.get("reference_text", [])
            
            if not ref_audio or not ref_text:
                print("❌ 启用语音复刻但缺少参考音频或文本")
                return False
            
            if len(ref_audio) != len(ref_text):
                print("❌ 参考音频和文本数量不匹配")
                return False
            
            # 检查音频文件是否存在
            for audio_file in ref_audio:
                audio_path = project_root / audio_file
                if not audio_path.exists():
                    print(f"❌ 参考音频文件不存在: {audio_path}")
                    return False
        
        print("✅ 配置验证通过")
        return True
    
    def print_config_info(self):
        """打印配置信息"""
        print("\n📋 当前配置信息:")
        print(f"   WebSocket URL: {self.demo_config['ws_url']}")
        print(f"   应用ID: {self.demo_config['appid'][:10]}...")
        print(f"   资源ID: {self.demo_config['resource_id']}")
        print(f"   默认音色: {self.demo_config['speaker']}")
        print(f"   语音复刻: {'启用' if self.demo_config['voice_cloning'] else '禁用'}")
        
        if self.demo_config.get("voice_cloning"):
            print(f"   参考音频数量: {len(self.demo_config['reference_audio'])}")
            for i, (audio, text) in enumerate(zip(
                self.demo_config["reference_audio"], 
                self.demo_config["reference_text"]
            ), 1):
                print(f"     {i}. {audio} -> {text[:30]}...")
    
    async def test_voice_cloning(self):
        """测试语音复刻功能"""
        print("\n🚀 开始测试语音复刻功能...")
        
        try:
            # 创建TTS提供者实例
            tts_provider = TTSProvider(self.demo_config, delete_audio_file=False)
            
            # 测试文本
            test_text = "这是一个语音复刻功能的测试，请听听效果如何。"
            
            print(f"📝 测试文本: {test_text}")
            print("🎵 开始生成音频...")
            
            # 生成音频
            audio_data = tts_provider.to_tts(test_text)
            
            if audio_data:
                print(f"✅ 音频生成成功，数据长度: {len(audio_data)}")
                
                # 保存音频文件用于测试
                output_dir = project_root / "demo" / "output"
                output_dir.mkdir(parents=True, exist_ok=True)
                
                output_file = output_dir / "voice_cloning_test.opus"
                with open(output_file, "wb") as f:
                    for opus_data in audio_data:
                        f.write(opus_data)
                
                print(f"💾 音频已保存到: {output_file}")
                print("🎉 语音复刻测试完成！")
            else:
                print("❌ 音频生成失败")
                
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {str(e)}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")
    
    def run_demo(self):
        """运行演示"""
        print("🎤 火山引擎语音复刻功能演示")
        print("=" * 50)
        
        # 创建演示资源
        self.create_demo_assets()
        
        # 打印配置信息
        self.print_config_info()
        
        # 验证配置
        if not self.validate_config():
            print("\n❌ 配置验证失败，请检查配置后重试")
            return
        
        # 询问是否继续测试
        print("\n⚠️  注意：此演示将调用火山引擎API，可能产生费用")
        user_input = input("是否继续测试？(y/N): ").strip().lower()
        
        if user_input in ['y', 'yes']:
            # 运行异步测试
            asyncio.run(self.test_voice_cloning())
        else:
            print("🛑 演示已取消")
        
        print("\n📚 更多信息请参考:")
        print("   - 火山引擎文档: https://www.volcengine.com/docs/6561/1329505")
        print("   - 项目配置文件: config.yaml")

def main():
    """主函数"""
    demo = VoiceCloningDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()
