-- 更新火山双流式TTS配置，添加语音复刻功能
-- 作者: AI Assistant
-- 日期: 2025-07-01

-- 更新火山双流式TTS供应器配置，添加语音复刻字段
UPDATE `ai_model_provider` 
SET `fields` = '[{"key":"ws_url","label":"WebSocket地址","type":"string"},{"key":"appid","label":"应用ID","type":"string"},{"key":"access_token","label":"访问令牌","type":"string"},{"key":"resource_id","label":"资源ID","type":"string"},{"key":"speaker","label":"默认音色","type":"string"},{"key":"voice_cloning","label":"启用语音复刻","type":"boolean"},{"key":"reference_audio","label":"参考音频文件","type":"array"},{"key":"reference_text","label":"参考文本","type":"array"}]'
WHERE `id` = 'SYSTEM_TTS_HSDSTTS';

-- 更新火山双流式TTS模型配置，添加语音复刻默认参数
UPDATE `ai_model_config` 
SET `config_json` = '{\"type\": \"huoshan_double_stream\", \"ws_url\": \"wss://openspeech.bytedance.com/api/v3/tts/bidirection\", \"appid\": \"你的火山引擎语音合成服务appid\", \"access_token\": \"你的火山引擎语音合成服务access_token\", \"resource_id\": \"volc.service_type.10029\", \"speaker\": \"zh_female_wanwanxiaohe_moon_bigtts\", \"voice_cloning\": false, \"reference_audio\": [], \"reference_text\": []}'
WHERE `id` = 'TTS_HuoshanDoubleStreamTTS';
