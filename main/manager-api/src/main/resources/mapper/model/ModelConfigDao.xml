<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="xiaozhi.modules.model.dao.ModelConfigDao">
    <resultMap id="ModelConfigResultMap" type="xiaozhi.modules.model.entity.ModelConfigEntity">
        <id column="id" property="id"/>
        <result column="model_type" property="modelType"/>
        <result column="model_code" property="modelCode"/>
        <result column="model_name" property="modelName"/>
        <result column="is_default" property="isDefault"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="config_json" property="configJson" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="doc_link" property="docLink"/>
        <result column="remark" property="remark"/>
        <result column="sort" property="sort"/>
        <result column="updater" property="updater"/>
        <result column="update_date" property="updateDate"/>
        <result column="creator" property="creator"/>
        <result column="create_date" property="createDate"/>
    </resultMap>
    <!-- 获取模型供应器字段 -->
    <select id="getModelCodeList" resultType="String">
        select model_name from ai_model_config where model_type = #{modelType}
            <if test="modelName != null">and model_name = #{modelName}</if>
    </select>
</mapper>