<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="xiaozhi.modules.sys.dao.SysDictDataDao">
    <select id="getDictDataByType" resultType="xiaozhi.modules.sys.vo.SysDictDataItem">
        SELECT d.dict_label AS `name`, d.dict_value AS `key`
        FROM sys_dict_data d
        LEFT JOIN sys_dict_type t ON d.dict_type_id = t.id
        WHERE t.dict_type = #{dictType}
        ORDER BY d.sort ASC
    </select>

    <select id="getTypeByTypeId" resultType="java.lang.String">
        SELECT dict_type
        FROM sys_dict_type
        WHERE id = #{dictTypeId}
    </select>
</mapper>