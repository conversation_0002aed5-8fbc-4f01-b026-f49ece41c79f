<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>离线模式 - 小智控制台</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background-color: #f5f7fa;
      color: #333;
    }
    .container {
      text-align: center;
      padding: 2rem;
      background: white;
      border-radius: 10px;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      max-width: 80%;
    }
    h1 {
      margin-bottom: 1rem;
      color: #409EFF;
    }
    p {
      margin-bottom: 1.5rem;
      line-height: 1.6;
    }
    .btn {
      background-color: #409EFF;
      color: white;
      border: none;
      padding: 0.8rem 1.5rem;
      border-radius: 4px;
      font-size: 1rem;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    .btn:hover {
      background-color: #337ecc;
    }
    .icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      color: #409EFF;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon">📶</div>
    <h1>您当前处于离线模式</h1>
    <p>看起来您的网络连接有问题，无法连接到小智控制台服务器。</p>
    <p>部分已缓存的内容和静态资源可能仍然可用，但功能可能受到限制。</p>
    <button class="btn" onclick="window.location.reload()">重新尝试连接</button>
  </div>

  <script>
    // 检测网络状态变化
    window.addEventListener('online', () => {
      window.location.reload();
    });
  </script>
</body>
</html> 