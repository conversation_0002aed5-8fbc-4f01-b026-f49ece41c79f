#!/usr/bin/env python3
"""
火山引擎双流式TTS快速测试脚本
"""

import asyncio
from huoshan_double_stream_enhanced import run_enhanced_tts

# 配置信息 - 请修改为您的实际配置
CONFIG = {
    "app_id": "1181316113",  # 替换为您的应用ID
    "token": "XliULxhgl1UU0zUkMuuTjzcJkOyryWLA",  # 替换为您的访问令牌
    "output_dir": "/Users/<USER>/Downloads/"  # 输出目录
}

async def test_preset_speaker():
    """测试预置发音人"""
    print("🎤 测试预置发音人...")
    
    try:
        await run_enhanced_tts(
            app_id=CONFIG["app_id"],
            token=CONFIG["token"],
            speaker='zh_female_wanwanxiaohe_moon_bigtts',
            text='你好，这是一个预置发音人的测试。今天天气很好，适合出门走走。',
            output_path=CONFIG["output_dir"] + "test_preset.wav",
            stream_audio=True,
            use_clone_speaker=False,
            split_long_text=True,
            max_segment_length=50
        )
        print("✅ 预置发音人测试成功！")
        return True
    except Exception as e:
        print(f"❌ 预置发音人测试失败: {e}")
        return False


async def test_clone_speaker():
    """测试复刻发音人"""
    print("🎤 测试复刻发音人...")
    
    # 注意：这里需要您提供实际的参考音频文件
    reference_audio_list = [
        # "/path/to/your/reference1.wav",  # 请替换为实际路径
        # "/path/to/your/reference2.wav"   # 请替换为实际路径
    ]
    
    reference_text_list = [
        # "参考音频1对应的文本",  # 必须与音频内容完全一致
        # "参考音频2对应的文本"   # 必须与音频内容完全一致
    ]
    
    # 如果没有参考音频，跳过测试
    if not reference_audio_list:
        print("⚠️ 跳过复刻发音人测试（需要提供参考音频）")
        print("请在脚本中配置reference_audio_list和reference_text_list")
        return False
    
    try:
        await run_enhanced_tts(
            app_id=CONFIG["app_id"],
            token=CONFIG["token"],
            speaker='',
            text='这是使用声音复刻技术生成的语音测试。',
            output_path=CONFIG["output_dir"] + "test_clone.wav",
            stream_audio=True,
            use_clone_speaker=True,
            clone_speaker_id='S_Faev7OGv1',  # 替换为您的复刻发音人ID
            post_process={"pitch": 0},
            reference_audio_list=reference_audio_list,
            reference_text_list=reference_text_list,
            split_long_text=True
        )
        print("✅ 复刻发音人测试成功！")
        return True
    except Exception as e:
        print(f"❌ 复刻发音人测试失败: {e}")
        return False


async def test_long_text():
    """测试长文本分段"""
    print("📝 测试长文本分段...")
    
    long_text = """
    人工智能技术的发展日新月异，语音合成作为其中的重要分支，已经在各个领域得到了广泛应用。
    从最初的机械式合成到现在的神经网络驱动，语音合成技术经历了巨大的变革。
    现代的语音合成系统不仅能够产生自然流畅的语音，还能够模拟不同的说话风格和情感表达。
    这项技术在个性化语音助手、有声读物制作、影视配音等领域都有着巨大的应用潜力。
    """
    
    try:
        await run_enhanced_tts(
            app_id=CONFIG["app_id"],
            token=CONFIG["token"],
            speaker='zh_female_wanwanxiaohe_moon_bigtts',
            text=long_text.strip(),
            output_path=CONFIG["output_dir"] + "test_long_text.wav",
            stream_audio=True,
            use_clone_speaker=False,
            split_long_text=True,
            max_segment_length=60
        )
        print("✅ 长文本分段测试成功！")
        return True
    except Exception as e:
        print(f"❌ 长文本分段测试失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行所有测试...")
    print("=" * 60)
    
    results = []
    
    # 测试预置发音人
    results.append(await test_preset_speaker())
    print("\n" + "="*60)
    
    # 测试复刻发音人
    results.append(await test_clone_speaker())
    print("\n" + "="*60)
    
    # 测试长文本分段
    results.append(await test_long_text())
    print("\n" + "="*60)
    
    # 输出测试结果
    print("\n📊 测试结果汇总:")
    test_names = ["预置发音人", "复刻发音人", "长文本分段"]
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    print(f"\n总计: {success_count}/{total_count} 个测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接")


def check_config():
    """检查配置"""
    print("🔧 检查配置...")
    
    issues = []
    
    if CONFIG["app_id"] == "1181316113":
        issues.append("请替换app_id为您的实际应用ID")
    
    if CONFIG["token"] == "XliULxhgl1UU0zUkMuuTjzcJkOyryWLA":
        issues.append("请替换token为您的实际访问令牌")
    
    import os
    if not os.path.exists(CONFIG["output_dir"]):
        issues.append(f"输出目录不存在: {CONFIG['output_dir']}")
    
    if issues:
        print("⚠️ 配置问题:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ 配置检查通过")
        return True


if __name__ == "__main__":
    print("🎤 火山引擎双流式TTS快速测试")
    print("=" * 60)
    
    # 检查配置
    if not check_config():
        print("\n❌ 请先修改配置后再运行测试")
        exit(1)
    
    try:
        print("\n请选择测试模式:")
        print("1. 预置发音人测试")
        print("2. 复刻发音人测试")
        print("3. 长文本分段测试")
        print("4. 运行所有测试")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            asyncio.run(test_preset_speaker())
        elif choice == "2":
            asyncio.run(test_clone_speaker())
        elif choice == "3":
            asyncio.run(test_long_text())
        elif choice == "4":
            asyncio.run(run_all_tests())
        elif choice == "5":
            print("👋 再见！")
            exit(0)
        else:
            print("❌ 无效选择，运行所有测试")
            asyncio.run(run_all_tests())
        
        print("\n✅ 测试完成！")
        print(f"📁 输出文件保存在: {CONFIG['output_dir']}")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
