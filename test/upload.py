import json
import uuid

import requests

group_id = "1939118475410678679"
api_key = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
url = f'https://api.minimaxi.com/v1/files/upload?GroupId={group_id}'
headers1 = {
    'authority': 'api.minimaxi.com',
    'Authorization': f'Bearer {api_key}'
}

data = {
    'purpose': 'voice_clone'
}

files = {
    'file': open('1.mp3', 'rb')
}
response = requests.post(url, headers=headers1, data=data, files=files)
print(response.json())
file_id = response.json().get("file").get("file_id")
print(file_id)



#音频复刻
url = f'https://api.minimaxi.com/v1/voice_clone?GroupId={group_id}'
payload2 = json.dumps({
  "file_id": file_id,
  "voice_id": "test12341123123123123"
})
headers2 = {
  'Authorization': f'Bearer {api_key}',
  'content-type': 'application/json'
}
response = requests.request("POST", url, headers=headers2, data=payload2)
print(response.text)