#!/usr/bin/env python3
"""
MiniMax TTS 多段文本处理脚本
按句号分割长文本，分别生成音频并合并
"""

import json
import requests
import os
import re
from datetime import datetime
from pydub import AudioSegment
from pydub.playback import play
from io import BytesIO


class MinimaxMultiSegmentTTS:
    """MiniMax TTS 多段文本处理器"""
    
    def __init__(self, group_id, api_key):
        self.group_id = group_id
        self.api_key = api_key
        self.api_url = f'https://api.minimaxi.com/v1/t2a_v2?GroupId={group_id}'
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def split_text_by_sentences(self, text, max_length=200):
        """
        按句号分割文本，并确保每段不超过最大长度
        
        Args:
            text (str): 要分割的文本
            max_length (int): 每段的最大长度
        
        Returns:
            list: 分割后的文本段落列表
        """
        # 清理文本
        text = text.strip()
        
        # 按句号、感叹号、问号分割
        sentences = re.split(r'[。！？.!?]', text)
        
        # 过滤空字符串
        sentences = [s.strip() for s in sentences if s.strip()]
        
        segments = []
        current_segment = ""
        
        for sentence in sentences:
            # 如果当前段落加上新句子不超过最大长度
            test_segment = current_segment + "。" + sentence if current_segment else sentence
            
            if len(test_segment) <= max_length:
                current_segment = test_segment
            else:
                # 保存当前段落并开始新段落
                if current_segment:
                    segments.append(current_segment + "。")
                current_segment = sentence
        
        # 添加最后一个段落
        if current_segment:
            segments.append(current_segment + "。")
        
        return segments
    
    def text_to_audio_bytes(self, text, voice_id="test12341123123123123", **kwargs):
        """
        将单段文本转换为音频字节数据
        
        Args:
            text (str): 要转换的文本
            voice_id (str): 音色ID
            **kwargs: 其他可选参数
        
        Returns:
            bytes: 音频字节数据，失败时返回 None
        """
        # 构建请求数据
        request_data = {
            "model": kwargs.get("model", "speech-02-hd"),
            "text": text,
            "stream": False,
            "voice_setting": {
                "voice_id": voice_id,
                "speed": kwargs.get("speed", 1),
                "vol": kwargs.get("vol", 1),
                "pitch": kwargs.get("pitch", 0),
                "emotion": kwargs.get("emotion", "happy")
            },
            "pronunciation_dict": kwargs.get("pronunciation_dict", {
                "tone": ["处理/(chu3)(li3)", "危险/dangerous"]
            }),
            "audio_setting": {
                "sample_rate": kwargs.get("sample_rate", 32000),
                "bitrate": kwargs.get("bitrate", 128000),
                "format": kwargs.get("format", "mp3"),
                "channel": kwargs.get("channel", 1)
            }
        }
        
        try:
            # 发送请求
            response = requests.post(
                self.api_url,
                headers=self.headers,
                data=json.dumps(request_data),
                timeout=30
            )
            
            # 检查响应状态
            if response.status_code != 200:
                print(f"❌ API请求失败: {response.status_code}")
                return None
            
            # 解析响应
            response_data = response.json()
            
            # 检查业务状态码
            if response_data.get("base_resp", {}).get("status_code") != 0:
                error_msg = response_data.get("base_resp", {}).get("status_msg", "未知错误")
                print(f"❌ 业务请求失败: {error_msg}")
                return None
            
            # 获取音频数据
            audio_hex = response_data.get("data", {}).get("audio")
            if not audio_hex:
                print("❌ 响应中没有音频数据")
                return None
            
            # 转换十六进制数据为字节
            audio_bytes = bytes.fromhex(audio_hex)
            return audio_bytes
            
        except Exception as e:
            print(f"❌ 处理过程中发生错误: {str(e)}")
            return None
    
    def process_multi_segment_text(self, text, output_dir="./audio_output", **kwargs):
        """
        处理多段文本，生成音频并合并
        
        Args:
            text (str): 长文本
            output_dir (str): 输出目录
            **kwargs: TTS参数
        
        Returns:
            str: 合并后的音频文件路径，失败时返回 None
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 分割文本
        text_segments = self.split_text_by_sentences(text)
        
        print(f"📝 文本已分割为 {len(text_segments)} 个段落:")
        for i, segment in enumerate(text_segments, 1):
            print(f"   {i}. {segment[:50]}{'...' if len(segment) > 50 else ''}")
        
        # 处理每个段落
        audio_segments = []
        
        for i, segment in enumerate(text_segments, 1):
            print(f"\n🎵 处理第 {i}/{len(text_segments)} 段...")
            print(f"   文本: {segment}")
            
            # 生成音频
            audio_bytes = self.text_to_audio_bytes(segment, **kwargs)
            
            if audio_bytes:
                try:
                    # 转换为AudioSegment对象
                    audio_segment = AudioSegment.from_file(BytesIO(audio_bytes), format="mp3")
                    audio_segments.append(audio_segment)
                    
                    print(f"✅ 第 {i} 段处理完成，音频长度: {len(audio_segment)}ms")
                except Exception as e:
                    print(f"❌ 第 {i} 段音频转换失败: {str(e)}")
            else:
                print(f"❌ 第 {i} 段音频生成失败")
        
        if not audio_segments:
            print("❌ 没有成功生成任何音频段落")
            return None
        
        # 合并所有音频段落
        print(f"\n🔗 正在合并 {len(audio_segments)} 个音频段落...")
        
        combined_audio = audio_segments[0]
        for audio_segment in audio_segments[1:]:
            # 在段落之间添加短暂停顿（800ms）
            silence = AudioSegment.silent(duration=800)
            combined_audio = combined_audio + silence + audio_segment
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"minimax_combined_{timestamp}.mp3"
        output_path = os.path.join(output_dir, output_filename)
        
        # 保存合并后的音频
        combined_audio.export(output_path, format="mp3")
        
        print(f"✅ 合并完成！")
        print(f"📁 文件路径: {output_path}")
        print(f"⏱️  总时长: {len(combined_audio) / 1000:.1f} 秒")
        print(f"📊 文件大小: {os.path.getsize(output_path):,} 字节")
        
        return output_path


def main():
    """主函数"""
    
    # API 配置
    group_id = "1939118475410678679"
    api_key = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    
    # 创建处理器实例
    tts_processor = MinimaxMultiSegmentTTS(group_id, api_key)
    
    # 长文本示例 - 您可以替换为任何长文本
    long_text = """
    真正的危险不是计算机开始像人一样思考，而是人开始像计算机一样思考。计算机只是可以帮我们处理一些简单事务。
    人工智能的发展正在改变我们的生活方式。从智能手机到自动驾驶汽车，AI技术无处不在。
    然而，我们需要保持理性的态度。技术是工具，关键在于如何使用它。
    教育和学习变得比以往任何时候都更加重要。我们需要不断适应和学习新技能。
    未来属于那些能够与技术和谐共处的人。让我们拥抱变化，迎接挑战。
    创新是推动社会进步的重要动力。每一次技术革命都带来了新的机遇和挑战。
    我们应该以开放的心态面对未来。保持好奇心，勇于探索未知的领域。
    """
    
    print("🚀 开始处理多段文本...")
    
    # 处理文本并生成合并音频
    output_file = tts_processor.process_multi_segment_text(
        text=long_text.strip(),
        output_dir="./audio_output",
        voice_id="test12341123123123123",
        model="speech-02-hd",
        speed=1,
        vol=1,
        pitch=0,
        emotion="happy"
    )
    
    if output_file:
        print(f"\n🎉 处理完成！合并音频文件: {output_file}")
        
        # 可选：播放合并后的音频
        try:
            print("\n🔊 正在播放合并后的音频...")
            audio = AudioSegment.from_file(output_file)
            play(audio)
        except Exception as e:
            print(f"播放音频时出错: {str(e)}")
            print("💡 您可以手动播放音频文件")
    else:
        print("\n💥 处理失败！")


if __name__ == "__main__":
    main()
