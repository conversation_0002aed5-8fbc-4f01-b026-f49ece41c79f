import asyncio
import websockets
import json
import ssl
import re
import os
from datetime import datetime
from pydub import AudioSegment  # 新增音频处理库
from pydub.playback import play
from io import BytesIO

MODULE = "speech-02-hd"
EMOTION = "happy"


async def establish_connection(api_key):
    """建立WebSocket连接"""
    url = "wss://api.minimaxi.com/ws/v1/t2a_v2"
    headers = {"Authorization": f"Bearer {api_key}"}

    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE

    try:
        ws = await websockets.connect(url, additional_headers=headers, ssl=ssl_context)
        connected = json.loads(await ws.recv())
        if connected.get("event") == "connected_success":
            print("连接成功")
            return ws
        return None
    except Exception as e:
        print(f"连接失败: {e}")
        return None


async def start_task(websocket, text):
    """发送任务开始请求"""
    start_msg = {
        "event": "task_start",
        "model": MODULE,
        "voice_setting": {
            "voice_id": "test12341123123123123",
            "speed": 1,
            "vol": 1,
            "pitch": 0,
            "emotion": EMOTION
        },
        "audio_setting": {
            "sample_rate": 32000,
            "bitrate": 128000,
            "format": "mp3",
            "channel": 1
        }
    }
    await websocket.send(json.dumps(start_msg))
    response = json.loads(await websocket.recv())
    return response.get("event") == "task_started"


async def continue_task(websocket, text):
    """发送继续请求并收集音频数据"""
    await websocket.send(json.dumps({
        "event": "task_continue",
        "text": text
    }))

    audio_chunks = []
    chunk_counter = 1  # 新增分块计数器
    while True:
        response = json.loads(await websocket.recv())
        if "data" in response and "audio" in response["data"]:
            audio = response["data"]["audio"]
            # 打印编码信息（前20字符 + 总长度）
            print(f"音频块 #{chunk_counter}")
            print(f"编码长度: {len(audio)} 字节")
            print(f"前20字符: {audio[:20]}...")
            print("-" * 40)

            audio_chunks.append(audio)
            chunk_counter += 1
        if response.get("is_final"):
            break
    return "".join(audio_chunks)


async def close_connection(websocket):
    """关闭连接"""
    if websocket:
        await websocket.send(json.dumps({"event": "task_finish"}))
        await websocket.close()
        print("连接已关闭")

def split_text_by_sentences(text, max_length=200):
    """
    按句号分割文本，并确保每段不超过最大长度

    Args:
        text (str): 要分割的文本
        max_length (int): 每段的最大长度

    Returns:
        list: 分割后的文本段落列表
    """
    # 按句号分割
    sentences = re.split(r'[。！？.!?]', text)

    # 过滤空字符串并添加标点符号
    sentences = [s.strip() for s in sentences if s.strip()]

    segments = []
    current_segment = ""

    for sentence in sentences:
        # 如果当前段落加上新句子不超过最大长度
        if len(current_segment + sentence) <= max_length:
            if current_segment:
                current_segment += "。" + sentence
            else:
                current_segment = sentence
        else:
            # 保存当前段落并开始新段落
            if current_segment:
                segments.append(current_segment + "。")
            current_segment = sentence

    # 添加最后一个段落
    if current_segment:
        segments.append(current_segment + "。")

    return segments


async def process_text_segments(api_key, text_segments, output_dir="./audio_output"):
    """
    处理多个文本段落，生成音频并合并

    Args:
        api_key (str): API密钥
        text_segments (list): 文本段落列表
        output_dir (str): 输出目录

    Returns:
        str: 合并后的音频文件路径
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    audio_segments = []

    print(f"📝 共有 {len(text_segments)} 个文本段落需要处理")

    for i, text_segment in enumerate(text_segments, 1):
        print(f"\n🎵 处理第 {i}/{len(text_segments)} 段:")
        print(f"   文本: {text_segment[:50]}{'...' if len(text_segment) > 50 else ''}")

        # 建立连接
        ws = await establish_connection(api_key)
        if not ws:
            print(f"❌ 第 {i} 段连接失败")
            continue

        try:
            # 启动任务
            if not await start_task(ws, text_segment[:10]):
                print(f"❌ 第 {i} 段任务启动失败")
                continue

            # 获取音频数据
            hex_audio = await continue_task(ws, text_segment)

            if hex_audio:
                # 转换为字节数据
                audio_bytes = bytes.fromhex(hex_audio)

                # 转换为AudioSegment对象
                audio_segment = AudioSegment.from_file(BytesIO(audio_bytes), format="mp3")
                audio_segments.append(audio_segment)

                print(f"✅ 第 {i} 段处理完成，音频长度: {len(audio_segment)}ms")
            else:
                print(f"❌ 第 {i} 段没有获取到音频数据")

        except Exception as e:
            print(f"❌ 第 {i} 段处理出错: {str(e)}")

        finally:
            await close_connection(ws)

    if not audio_segments:
        print("❌ 没有成功生成任何音频段落")
        return None

    # 合并所有音频段落
    print(f"\n🔗 正在合并 {len(audio_segments)} 个音频段落...")

    combined_audio = audio_segments[0]
    for audio_segment in audio_segments[1:]:
        # 在段落之间添加短暂停顿（500ms）
        silence = AudioSegment.silent(duration=500)
        combined_audio = combined_audio + silence + audio_segment

    # 生成输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"combined_tts_{timestamp}.mp3"
    output_path = os.path.join(output_dir, output_filename)

    # 保存合并后的音频
    combined_audio.export(output_path, format="mp3")

    print(f"✅ 合并完成！")
    print(f"📁 文件路径: {output_path}")
    print(f"⏱️  总时长: {len(combined_audio) / 1000:.1f} 秒")
    print(f"📊 文件大小: {os.path.getsize(output_path):,} 字节")

    return output_path


async def main():
    API_KEY = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

    # 长文本示例 - 您可以替换为任何长文本
    LONG_TEXT = """
    真正的危险不是计算机开始像人一样思考，而是人开始像计算机一样思考。计算机只是可以帮我们处理一些简单事务。
    人工智能的发展正在改变我们的生活方式。从智能手机到自动驾驶汽车，AI技术无处不在。
    然而，我们需要保持理性的态度。技术是工具，关键在于如何使用它。
    教育和学习变得比以往任何时候都更加重要。我们需要不断适应和学习新技能。
    未来属于那些能够与技术和谐共处的人。让我们拥抱变化，迎接挑战。
    """

    # 按句号分割文本
    text_segments = split_text_by_sentences(LONG_TEXT.strip())

    print("📋 文本分割结果:")
    for i, segment in enumerate(text_segments, 1):
        print(f"   {i}. {segment}")

    # 处理所有文本段落
    output_file = await process_text_segments(API_KEY, text_segments)

    if output_file:
        print(f"\n🎉 所有处理完成！最终音频文件: {output_file}")

        # 可选：播放合并后的音频
        try:
            print("\n🔊 正在播放合并后的音频...")
            audio = AudioSegment.from_file(output_file)
            play(audio)
        except Exception as e:
            print(f"播放音频时出错: {str(e)}")
    else:
        print("\n💥 处理失败！")


if __name__ == "__main__":
    asyncio.run(main())