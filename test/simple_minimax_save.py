#!/usr/bin/env python3
"""
简单的 MiniMax TTS 音频保存脚本
直接使用您提供的 curl 命令参数
"""

import json
import requests
import os
from datetime import datetime


def save_minimax_tts_audio():
    """使用 MiniMax API 生成并保存音频文件"""
    
    # API 配置（来自您的 curl 命令）
    url = 'https://api.minimaxi.com/v1/t2a_v2?GroupId=1939118475410678679'
    headers = {
        'Authorization': 'Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        'Content-Type': 'application/json'
    }
    
    # 请求数据（来自您的 curl 命令）
    data = {
        "model": "speech-02-hd",
        "text": "真正的危险不是计算机开始像人一样思考，而是人开始像计算机一样思考。计算机只是可以帮我们处理一些简单事务。",
        "stream": False,
        "voice_setting": {
            "voice_id": "test12341123123123123",
            "speed": 1,
            "vol": 1,
            "pitch": 0,
            "emotion": "happy"
        },
        "pronunciation_dict": {
            "tone": ["处理/(chu3)(li3)", "危险/dangerous"]
        },
        "audio_setting": {
            "sample_rate": 32000,
            "bitrate": 128000,
            "format": "mp3",
            "channel": 1
        }
    }
    
    try:
        print("🎵 正在调用 MiniMax TTS API...")
        
        # 发送请求
        response = requests.post(url, headers=headers, json=data)
        
        # 检查 HTTP 状态码
        if response.status_code != 200:
            print(f"❌ HTTP 请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
        
        # 解析 JSON 响应
        response_json = response.json()
        print("📋 API 响应结构:")
        print(json.dumps(response_json, indent=2, ensure_ascii=False))
        
        # 检查业务状态码
        base_resp = response_json.get("base_resp", {})
        if base_resp.get("status_code") != 0:
            print(f"❌ 业务请求失败: {base_resp.get('status_msg', '未知错误')}")
            return None
        
        # 获取音频数据
        audio_data = response_json.get("data", {})
        audio_hex = audio_data.get("audio")
        
        if not audio_hex:
            print("❌ 响应中没有找到音频数据")
            return None
        
        # 转换十六进制字符串为字节数据
        audio_bytes = bytes.fromhex(audio_hex)
        
        # 创建输出目录
        output_dir = "./audio_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"minimax_tts_{timestamp}.mp3"
        file_path = os.path.join(output_dir, filename)
        
        # 保存音频文件
        with open(file_path, "wb") as audio_file:
            audio_file.write(audio_bytes)
        
        print(f"✅ 音频文件已成功保存!")
        print(f"📁 文件路径: {file_path}")
        print(f"📊 文件大小: {len(audio_bytes):,} 字节")
        print(f"⏱️  音频时长: 约 {len(audio_bytes) / (32000 * 2):.1f} 秒")
        
        return file_path
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON 解析错误: {e}")
        print(f"原始响应: {response.text}")
        return None
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")
        return None


def main():
    """主函数"""
    print("🚀 开始 MiniMax TTS 音频生成...")
    
    saved_file = save_minimax_tts_audio()
    
    if saved_file:
        print(f"\n🎉 完成！音频已保存到: {saved_file}")
        print("\n💡 提示:")
        print("   - 可以使用任何音频播放器播放生成的 MP3 文件")
        print("   - 如需修改文本，请编辑脚本中的 'text' 字段")
        print("   - 如需修改音色，请修改 'voice_id' 字段")
    else:
        print("\n💥 音频生成失败，请检查 API 配置和网络连接")


if __name__ == "__main__":
    main()
